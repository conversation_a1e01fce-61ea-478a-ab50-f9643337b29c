package com.mc.tool.caesar.vpm.validation.constraints.impl;

import com.mc.tool.caesar.api.datamodel.SystemConfigData;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.vpm.validation.constraints.UniqueVirtualIp;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 验证虚拟IP不能与网络配置1和网络配置3的IP地址相同的实现类.
 * 此验证器应用于整个Bean，验证虚拟IP字段.
 */
public class UniqueVirtualIpImpl implements ConstraintValidator<UniqueVirtualIp, Object> {

  @Override
  public void initialize(UniqueVirtualIp constraintAnnotation) {
    // 初始化方法，暂时不需要特殊处理
  }

  @Override
  public boolean isValid(Object bean, ConstraintValidatorContext context) {
    if (bean == null) {
      return true;
    }
    try {
      // 获取systemConfigData
      SystemConfigData systemConfigData = getSystemConfigData(bean);
      if (systemConfigData == null) {
        return true; // 无法获取systemConfigData，跳过验证
      }
      // 获取虚拟IP
      String virtualIp = getVirtualIp(bean);
      if (virtualIp == null || virtualIp.trim().isEmpty()) {
        return true; // 空值由其他验证器处理
      }
      // 获取网络配置1的IP地址
      String networkIp1 = null;
      if (systemConfigData.getNetworkDataPreset1() != null) {
        networkIp1 = IpUtil.getAddressString(systemConfigData.getNetworkDataPreset1().getAddress());
      }
      // 获取网络配置3的IP地址
      String networkIp3 = null;
      if (systemConfigData.getNetworkDataPreset3() != null) {
        networkIp3 = IpUtil.getAddressString(systemConfigData.getNetworkDataPreset3().getAddress());
      }
      // 检查虚拟IP是否与网络配置1或3的IP相同
      if (virtualIp.equals(networkIp1) || virtualIp.equals(networkIp3)) {
        // 自定义错误消息，指向virtualIp字段
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
            .addPropertyNode("virtualIp")
            .addConstraintViolation();
        return false;
      }
      return true;
    } catch (Exception ex) {
      // 发生异常时，跳过验证
      return true;
    }
  }

  /**
   * 通过反射获取SystemConfigData对象.
   */
  private SystemConfigData getSystemConfigData(Object bean) {
    try {
      // 尝试直接访问systemConfigData字段
      Field field = bean.getClass().getDeclaredField("systemConfigData");
      field.setAccessible(true);
      return (SystemConfigData) field.get(bean);
    } catch (Exception ex) {
      // 忽略异常
    }
    return null;
  }

  /**
   * 通过反射获取虚拟IP值.
   */
  private String getVirtualIp(Object bean) {
    try {
      // 尝试调用getVirtualIp方法
      Method getVirtualIpMethod = bean.getClass().getMethod("getVirtualIp");
      return (String) getVirtualIpMethod.invoke(bean);
    } catch (Exception ex) {
      // 忽略异常
    }
    try {
      // 尝试直接访问virtualIp字段
      Field field = bean.getClass().getDeclaredField("virtualIp");
      field.setAccessible(true);
      return (String) field.get(bean);
    } catch (Exception ex) {
      // 忽略异常
    }
    return null;
  }
}
